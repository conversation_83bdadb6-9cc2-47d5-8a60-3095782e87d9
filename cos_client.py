from qcloud_cos import CosConfig
from qcloud_cos import CosS3Client
import os
import uuid
from datetime import datetime

class COSClient:
    def __init__(self, config):
        self.config = CosConfig(
            Region=config['region'],
            SecretId=config['secret_id'],
            SecretKey=config['secret_key']
        )
        self.client = CosS3Client(self.config)
        self.bucket = config['bucket']
    
    def upload_file(self, file_obj, file_type='audio'):
        """
        上传文件到COS
        :param file_obj: 文件对象
        :param file_type: 文件类型 ('audio' 或 'image')
        :return: 文件URL
        """
        try:
            # 生成唯一文件名
            file_ext = os.path.splitext(file_obj.filename)[1]
            unique_filename = f"{uuid.uuid4().hex}{file_ext}"
            
            # 根据文件类型设置路径
            if file_type == 'audio':
                key = f"audios/{datetime.now().strftime('%Y/%m')}/{unique_filename}"
            else:
                key = f"images/{datetime.now().strftime('%Y/%m')}/{unique_filename}"
            
            # 上传文件
            response = self.client.upload_fileobj(
                Bucket=self.bucket,
                Key=key,
                Fileobj=file_obj,
                EnableMD5=False
            )
            
            # 返回文件URL
            file_url = f"https://{self.bucket}.cos.{self.config.Region}.myqcloud.com/{key}"
            return {
                'success': True,
                'url': file_url,
                'key': key
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def delete_file(self, key):
        """删除COS文件"""
        try:
            self.client.delete_object(
                Bucket=self.bucket,
                Key=key
            )
            return True
        except Exception as e:
            print(f"删除文件失败: {e}")
            return False