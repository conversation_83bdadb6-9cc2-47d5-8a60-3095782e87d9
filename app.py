from flask import Flask, request, jsonify, render_template
from flask_cors import CORS
import sqlite3
import os
from datetime import datetime
from cos_client import COSClient
import uuid

from flask import Flask, render_template
from flask_cors import CORS
from flask_sqlalchemy import SQLAlchemy
from flask_migrate import Migrate
from flask_jwt_extended import J<PERSON>TManager
from config import Config

# 全局扩展对象
db = SQLAlchemy()
migrate = Migrate()
jwt = JWTManager()


def create_app(config_class=Config):
    """创建Flask应用实例"""
    app = Flask(__name__)
    app.config.from_object(config_class)

    # 初始化扩展
    db.init_app(app)
    migrate.init_app(app, db)
    jwt.init_app(app)
    CORS(app)

    # 注册蓝图
    from routes.audios import audios_bp
    from routes.themes import themes_bp

    app.register_blueprint(audios_bp, url_prefix='/api/v1/audios')
    app.register_blueprint(themes_bp, url_prefix='/api/v1/themes')

    # 静态文件服务 - 用于提供上传的文件

    @app.route("/privacy")
    def privacy_policy():
        return render_template('privacy.html', app_name='酣睡熊猫')

    @app.route("/useragreement")
    def user_agreement():
        return render_template('useragreement.html', app_name='酣睡熊猫')

    @app.route("/admin")
    def admin():
        return render_template('admin.html')

    # 创建数据库表
    with app.app_context():
        db.create_all()

    return app


app = Flask(__name__)
CORS(app)

# 配置
DATABASE = 'pandazzz.db'
COS_CONFIG = {
    'secret_id': 'AKIDa6ScbQ3TilbEj2A5mIeqpaAYjgPUeu32',
    'secret_key': 'sjX9iDWegicUVO6dw2xhFakkYCXjhRyJ', 
    'region': 'ap-guangzhou',
    'bucket': 'pandazzz-1251583259'
}

cos_client = COSClient(COS_CONFIG)

def init_db():
    """初始化数据库"""
    conn = sqlite3.connect(DATABASE)
    cursor = conn.cursor()
    
    # 创建音频主题表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS audio_themes (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            title VARCHAR(100) NOT NULL,
            description TEXT,
            cover_image VARCHAR(255),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # 创建音频表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS audios (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            title VARCHAR(100) NOT NULL,
            type VARCHAR(20) CHECK(type IN ('dream', 'melody')),
            theme_id INTEGER,
            file_path VARCHAR(255),
            duration INTEGER,
            cover_image VARCHAR(255),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (theme_id) REFERENCES audio_themes(id)
        )
    ''')
    
    conn.commit()
    conn.close()

# if __name__ == '__main__':
#     init_db()
#     app.run(debug=True)

