from flask import Blueprint, request, jsonify
import sqlite3
from app import DATABASE, cos_client
import mutagen
from mutagen.mp3 import MP3

audios_bp = Blueprint('audios', __name__)

@audios_bp.route('/api/audios', methods=['GET'])
def get_audios():
    """获取音频列表"""
    audio_type = request.args.get('type')  # 'dream' 或 'melody'
    theme_id = request.args.get('theme_id')
    
    conn = sqlite3.connect(DATABASE)
    cursor = conn.cursor()
    
    query = '''
        SELECT a.id, a.title, a.type, a.theme_id, a.file_path, 
               a.duration, a.cover_image, a.created_at, t.title as theme_title
        FROM audios a
        LEFT JOIN audio_themes t ON a.theme_id = t.id
        WHERE 1=1
    '''
    params = []
    
    if audio_type:
        query += ' AND a.type = ?'
        params.append(audio_type)
    
    if theme_id:
        query += ' AND a.theme_id = ?'
        params.append(theme_id)
    
    query += ' ORDER BY a.created_at DESC'
    
    cursor.execute(query, params)
    
    audios = []
    for row in cursor.fetchall():
        audios.append({
            'id': row[0],
            'title': row[1],
            'type': row[2],
            'theme_id': row[3],
            'file_path': row[4],
            'duration': row[5],
            'cover_image': row[6],
            'created_at': row[7],
            'theme_title': row[8]
        })
    
    conn.close()
    return jsonify({'success': True, 'data': audios})

@audios_bp.route('/api/audios/upload', methods=['POST'])
def upload_audio():
    """上传音频文件"""
    if 'audio_file' not in request.files:
        return jsonify({'success': False, 'message': '没有选择音频文件'})
    
    audio_file = request.files['audio_file']
    cover_file = request.files.get('cover_file')
    
    title = request.form.get('title')
    audio_type = request.form.get('type')  # 'dream' 或 'melody'
    theme_id = request.form.get('theme_id')
    
    if not title or not audio_type:
        return jsonify({'success': False, 'message': '标题和类型不能为空'})
    
    # 上传音频文件
    audio_result = cos_client.upload_file(audio_file, 'audio')
    if not audio_result['success']:
        return jsonify({'success': False, 'message': f'音频上传失败: {audio_result["error"]}'})
    
    # 上传封面图片（如果有）
    cover_url = ''
    if cover_file:
        cover_result = cos_client.upload_file(cover_file, 'image')
        if cover_result['success']:
            cover_url = cover_result['url']
    
    # 获取音频时长
    duration = 0
    try:
        audio_file.seek(0)  # 重置文件指针
        audio_info = MP3(audio_file)
        duration = int(audio_info.info.length)
    except:
        pass
    
    # 保存到数据库
    conn = sqlite3.connect(DATABASE)
    cursor = conn.cursor()
    
    cursor.execute('''
        INSERT INTO audios (title, type, theme_id, file_path, duration, cover_image)
        VALUES (?, ?, ?, ?, ?, ?)
    ''', (
        title, 
        audio_type, 
        theme_id if theme_id else None,
        audio_result['url'],
        duration,
        cover_url
    ))
    
    audio_id = cursor.lastrowid
    conn.commit()
    conn.close()
    
    return jsonify({'success': True, 'id': audio_id})

@audios_bp.route('/api/audios/<int:audio_id>', methods=['DELETE'])
def delete_audio(audio_id):
    """删除音频文件"""
    conn = sqlite3.connect(DATABASE)
    cursor = conn.cursor()
    
    # 获取文件信息
    cursor.execute('SELECT file_path, cover_image FROM audios WHERE id = ?', (audio_id,))
    result = cursor.fetchone()
    
    if not result:
        conn.close()
        return jsonify({'success': False, 'message': '音频不存在'})
    
    file_path, cover_image = result
    
    # 删除数据库记录
    cursor.execute('DELETE FROM audios WHERE id = ?', (audio_id,))
    conn.commit()
    conn.close()
    
    # 删除COS文件（可选，根据需要决定是否立即删除）
    # cos_client.delete_file(file_path)
    # if cover_image:
    #     cos_client.delete_file(cover_image)
    
    return jsonify({'success': True})