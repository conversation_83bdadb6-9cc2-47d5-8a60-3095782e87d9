from flask import Blueprint, request, jsonify
import sqlite3
from datetime import datetime
from app import DATABASE, cos_client

themes_bp = Blueprint('themes', __name__)

@themes_bp.route('/api/themes', methods=['GET'])
def get_themes():
    """获取所有音频主题"""
    conn = sqlite3.connect(DATABASE)
    cursor = conn.cursor()
    
    cursor.execute('''
        SELECT id, title, description, cover_image, created_at, updated_at
        FROM audio_themes ORDER BY created_at DESC
    ''')
    
    themes = []
    for row in cursor.fetchall():
        themes.append({
            'id': row[0],
            'title': row[1],
            'description': row[2],
            'cover_image': row[3],
            'created_at': row[4],
            'updated_at': row[5]
        })
    
    conn.close()
    return jsonify({'success': True, 'data': themes})

@themes_bp.route('/api/themes', methods=['POST'])
def create_theme():
    """创建音频主题"""
    data = request.get_json()
    
    if not data.get('title'):
        return jsonify({'success': False, 'message': '主题标题不能为空'})
    
    conn = sqlite3.connect(DATABASE)
    cursor = conn.cursor()
    
    cursor.execute('''
        INSERT INTO audio_themes (title, description, cover_image)
        VALUES (?, ?, ?)
    ''', (data['title'], data.get('description', ''), data.get('cover_image', '')))
    
    theme_id = cursor.lastrowid
    conn.commit()
    conn.close()
    
    return jsonify({'success': True, 'id': theme_id})

@themes_bp.route('/api/themes/<int:theme_id>', methods=['PUT'])
def update_theme(theme_id):
    """更新音频主题"""
    data = request.get_json()
    
    conn = sqlite3.connect(DATABASE)
    cursor = conn.cursor()
    
    cursor.execute('''
        UPDATE audio_themes 
        SET title = ?, description = ?, cover_image = ?, updated_at = ?
        WHERE id = ?
    ''', (
        data['title'], 
        data.get('description', ''), 
        data.get('cover_image', ''),
        datetime.now().isoformat(),
        theme_id
    ))
    
    conn.commit()
    conn.close()
    
    return jsonify({'success': True})

@themes_bp.route('/api/themes/<int:theme_id>', methods=['DELETE'])
def delete_theme(theme_id):
    """删除音频主题"""
    conn = sqlite3.connect(DATABASE)
    cursor = conn.cursor()
    
    # 检查是否有关联的音频
    cursor.execute('SELECT COUNT(*) FROM audios WHERE theme_id = ?', (theme_id,))
    audio_count = cursor.fetchone()[0]
    
    if audio_count > 0:
        conn.close()
        return jsonify({'success': False, 'message': '该主题下还有音频文件，无法删除'})
    
    cursor.execute('DELETE FROM audio_themes WHERE id = ?', (theme_id,))
    conn.commit()
    conn.close()
    
    return jsonify({'success': True})