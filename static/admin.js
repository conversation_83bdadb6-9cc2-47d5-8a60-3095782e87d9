// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    loadThemes();
    loadAudios();
    
    // 音频类型变化时显示/隐藏主题选择
    document.getElementById('audio-type').addEventListener('change', function() {
        const themeGroup = document.getElementById('theme-select-group');
        if (this.value === 'dream') {
            themeGroup.style.display = 'block';
            loadThemesForSelect();
        } else {
            themeGroup.style.display = 'none';
        }
    });
    
    // 类型筛选
    document.getElementById('type-filter').addEventListener('change', function() {
        loadAudios();
    });
});

// 加载主题列表
async function loadThemes() {
    try {
        const response = await fetch('/api/themes');
        const result = await response.json();
        
        if (result.success) {
            displayThemes(result.data);
        }
    } catch (error) {
        console.error('加载主题失败:', error);
    }
}

// 显示主题列表
function displayThemes(themes) {
    const container = document.getElementById('themes-list');
    container.innerHTML = '';
    
    themes.forEach(theme => {
        const themeCard = document.createElement('div');
        themeCard.className = 'card mb-3';
        themeCard.innerHTML = `
            <div class="card-body">
                <div class="row">
                    <div class="col-md-2">
                        ${theme.cover_image ? `<img src="${theme.cover_image}" class="img-fluid rounded">` : '<div class="bg-light rounded" style="height: 80px;"></div>'}
                    </div>
                    <div class="col-md-8">
                        <h5>${theme.title}</h5>
                        <p class="text-muted">${theme.description || '暂无描述'}</p>
                        <small class="text-muted">创建时间: ${new Date(theme.created_at).toLocaleString()}</small>
                    </div>
                    <div class="col-md-2 text-end">
                        <button class="btn btn-sm btn-outline-danger" onclick="deleteTheme(${theme.id})">删除</button>
                    </div>
                </div>
            </div>
        `;
        container.appendChild(themeCard);
    });
}

// 显示主题模态框
function showThemeModal() {
    document.getElementById('theme-form').reset();
    new bootstrap.Modal(document.getElementById('themeModal')).show();
}

// 保存主题
async function saveTheme() {
    const title = document.getElementById('theme-title').value;
    const description = document.getElementById('theme-description').value;
    const coverFile = document.getElementById('theme-cover').files[0];
    
    if (!title) {
        alert('请输入主题标题');
        return;
    }
    
    let coverUrl = '';
    if (coverFile) {
        // 这里可以先上传图片，获取URL
        // 为简化示例，直接使用表单数据
    }
    
    try {
        const response = await fetch('/api/themes', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                title: title,
                description: description,
                cover_image: coverUrl
            })
        });
        
        const result = await response.json();
        if (result.success) {
            bootstrap.Modal.getInstance(document.getElementById('themeModal')).hide();
            loadThemes();
        } else {
            alert('保存失败: ' + result.message);
        }
    } catch (error) {
        alert('保存失败: ' + error.message);
    }
}

// 删除主题
async function deleteTheme(themeId) {
    if (!confirm('确定要删除这个主题吗？')) return;
    
    try {
        const response = await fetch(`/api/themes/${themeId}`, {
            method: 'DELETE'
        });
        
        const result = await response.json();
        if (result.success) {
            loadThemes();
        } else {
            alert('删除失败: ' + result.message);
        }
    } catch (error) {
        alert('删除失败: ' + error.message);
    }
}

// 加载音频列表
async function loadAudios() {
    const typeFilter = document.getElementById('type-filter').value;
    const url = typeFilter ? `/api/audios?type=${typeFilter}` : '/api/audios';
    
    try {
        const response = await fetch(url);
        const result = await response.json();
        
        if (result.success) {
            displayAudios(result.data);
        }
    } catch (error) {
        console.error('加载音频失败:', error);
    }
}

// 显示音频列表
function displayAudios(audios) {
    const container = document.getElementById('audios-list');
    container.innerHTML = '';
    
    audios.forEach(audio => {
        const audioCard = document.createElement('div');
        audioCard.className = 'card mb-3';
        audioCard.innerHTML = `
            <div class="card-body">
                <div class="row">
                    <div class="col-md-2">
                        ${audio.cover_image ? `<img src="${audio.cover_image}" class="img-fluid rounded">` : '<div class="bg-light rounded" style="height: 80px;"></div>'}
                    </div>
                    <div class="col-md-8">
                        <h5>${audio.title}</h5>
                        <p class="text-muted">
                            类型: ${audio.type === 'dream' ? '梦境音乐' : '简单旋律'}
                            ${audio.theme_title ? ` | 主题: ${audio.theme_title}` : ''}
                            ${audio.duration ? ` | 时长: ${Math.floor(audio.duration / 60)}:${(audio.duration % 60).toString().padStart(2, '0')}` : ''}
                        </p>
                        <audio controls style="width: 100%;">
                            <source src="${audio.file_path}" type="audio/mpeg">
                        </audio>
                        <br><small class="text-muted">上传时间: ${new Date(audio.created_at).toLocaleString()}</small>
                    </div>
                    <div class="col-md-2 text-end">
                        <button class="btn btn-sm btn-outline-danger" onclick="deleteAudio(${audio.id})">删除</button>
                    </div>
                </div>
            </div>
        `;
        container.appendChild(audioCard);
    });
}

// 显示音频上传模态框
function showAudioModal() {
    document.getElementById('audio-form').reset();
    document.getElementById('theme-select-group').style.display = 'none';
    new bootstrap.Modal(document.getElementById('audioModal')).show();
}

// 为选择框加载主题
async function loadThemesForSelect() {
    try {
        const response = await fetch('/api/themes');
        const result = await response.json();
        
        if (result.success) {
            const select = document.getElementById('audio-theme');
            select.innerHTML = '<option value="">请选择主题</option>';
            
            result.data.forEach(theme => {
                const option = document.createElement('option');
                option.value = theme.id;
                option.textContent = theme.title;
                select.appendChild(option);
            });
        }
    } catch (error) {
        console.error('加载主题失败:', error);
    }
}

// 上传音频
async function uploadAudio() {
    const formData = new FormData();
    
    const title = document.getElementById('audio-title').value;
    const type = document.getElementById('audio-type').value;
    const themeId = document.getElementById('audio-theme').value;
    const audioFile = document.getElementById('audio-file').files[0];
    const coverFile = document.getElementById('audio-cover').files[0];
    
    if (!title || !type || !audioFile) {
        alert('请填写必要信息并选择音频文件');
        return;
    }
    
    formData.append('title', title);
    formData.append('type', type);
    if (themeId) formData.append('theme_id', themeId);
    formData.append('audio_file', audioFile);
    if (coverFile) formData.append('cover_file', coverFile);
    
    try {
        const response = await fetch('/api/audios/upload', {
            method: 'POST',
            body: formData
        });
        
        const result = await response.json();
        if (result.success) {
            bootstrap.Modal.getInstance(document.getElementById('audioModal')).hide();
            loadAudios();
        } else {
            alert('上传失败: ' + result.message);
        }
    } catch (error) {
        alert('上传失败: ' + error.message);
    }
}

// 删除音频
async function deleteAudio(audioId) {
    if (!confirm('确定要删除这个音频吗？')) return;
    
    try {
        const response = await fetch(`/api/audios/${audioId}`, {
            method: 'DELETE'
        });
        
        const result = await response.json();
        if (result.success) {
            loadAudios();
        } else {
            alert('删除失败: ' + result.message);
        }
    } catch (error) {
        alert('删除失败: ' + error.message);
    }
}